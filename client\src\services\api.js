import axios from 'axios'

// Create axios instance
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getMe: () => api.get('/auth/me'),
  updateProfile: (userData) => api.put('/auth/profile', userData),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
}

// Resume API
export const resumeAPI = {
  upload: (formData, onUploadProgress) => 
    api.post('/resumes/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    }),
  getAll: (params) => api.get('/resumes', { params }),
  getById: (id) => api.get(`/resumes/${id}`),
  getStatus: (id) => api.get(`/resumes/${id}/status`),
  reprocess: (id) => api.post(`/resumes/${id}/reprocess`),
  getMatches: (id, params) => api.get(`/resumes/${id}/matches`, { params }),
  delete: (id) => api.delete(`/resumes/${id}`),
}

// Job API
export const jobAPI = {
  getAll: (params) => api.get('/jobs', { params }),
  getById: (id) => api.get(`/jobs/${id}`),
  create: (jobData) => api.post('/jobs', jobData),
  update: (id, jobData) => api.put(`/jobs/${id}`, jobData),
  delete: (id) => api.delete(`/jobs/${id}`),
  activate: (id) => api.post(`/jobs/${id}/activate`),
  deactivate: (id) => api.post(`/jobs/${id}/deactivate`),
  getMatches: (id, params) => api.get(`/jobs/${id}/matches`, { params }),
}

// Match API
export const matchAPI = {
  getByResume: (resumeId, params) => api.get(`/matches/resume/${resumeId}`, { params }),
  getUserMatches: (params) => api.get('/matches/user', { params }),
  getStats: () => api.get('/matches/stats'),
  refresh: (params) => api.post('/matches/refresh', null, { params }),
  deleteByResume: (resumeId) => api.delete(`/matches/resume/${resumeId}`),
}

// Health check
export const healthAPI = {
  check: () => api.get('/health'),
}

export default api
