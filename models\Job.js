const { query } = require('../config/database');

class Job {
  constructor(jobData) {
    this.id = jobData.id;
    this.title = jobData.title;
    this.company = jobData.company;
    this.description = jobData.description;
    this.requirements = jobData.requirements;
    this.location = jobData.location;
    this.salaryMin = jobData.salary_min;
    this.salaryMax = jobData.salary_max;
    this.jobType = jobData.job_type;
    this.experienceLevel = jobData.experience_level;
    this.embedding = jobData.embedding;
    this.isActive = jobData.is_active;
    this.createdAt = jobData.created_at;
    this.updatedAt = jobData.updated_at;
  }

  // Create a new job
  static async create({
    title,
    company,
    description,
    requirements,
    location,
    salaryMin,
    salaryMax,
    jobType,
    experienceLevel
  }) {
    const result = await query(
      `INSERT INTO jobs (
        title, company, description, requirements, location, 
        salary_min, salary_max, job_type, experience_level, 
        is_active, created_at, updated_at
      )
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, true, NOW(), NOW())
       RETURNING *`,
      [title, company, description, requirements, location, salaryMin, salaryMax, jobType, experienceLevel]
    );

    return new Job(result.rows[0]);
  }

  // Find job by ID
  static async findById(id) {
    const result = await query(
      'SELECT * FROM jobs WHERE id = $1',
      [id]
    );

    return result.rows.length > 0 ? new Job(result.rows[0]) : null;
  }

  // Find all active jobs
  static async findActive(limit = 20, offset = 0) {
    const result = await query(
      `SELECT * FROM jobs WHERE is_active = true 
       ORDER BY created_at DESC 
       LIMIT $1 OFFSET $2`,
      [limit, offset]
    );

    return result.rows.map(row => new Job(row));
  }

  // Search jobs by criteria
  static async search({
    title,
    company,
    location,
    jobType,
    experienceLevel,
    salaryMin,
    salaryMax,
    limit = 20,
    offset = 0
  }) {
    let whereConditions = ['is_active = true'];
    let params = [];
    let paramCount = 1;

    if (title) {
      whereConditions.push(`title ILIKE $${paramCount}`);
      params.push(`%${title}%`);
      paramCount++;
    }

    if (company) {
      whereConditions.push(`company ILIKE $${paramCount}`);
      params.push(`%${company}%`);
      paramCount++;
    }

    if (location) {
      whereConditions.push(`location ILIKE $${paramCount}`);
      params.push(`%${location}%`);
      paramCount++;
    }

    if (jobType) {
      whereConditions.push(`job_type = $${paramCount}`);
      params.push(jobType);
      paramCount++;
    }

    if (experienceLevel) {
      whereConditions.push(`experience_level = $${paramCount}`);
      params.push(experienceLevel);
      paramCount++;
    }

    if (salaryMin) {
      whereConditions.push(`salary_max >= $${paramCount}`);
      params.push(salaryMin);
      paramCount++;
    }

    if (salaryMax) {
      whereConditions.push(`salary_min <= $${paramCount}`);
      params.push(salaryMax);
      paramCount++;
    }

    params.push(limit, offset);

    const result = await query(
      `SELECT * FROM jobs 
       WHERE ${whereConditions.join(' AND ')}
       ORDER BY created_at DESC 
       LIMIT $${paramCount} OFFSET $${paramCount + 1}`,
      params
    );

    return result.rows.map(row => new Job(row));
  }

  // Update job embedding
  async updateEmbedding(embedding) {
    const result = await query(
      `UPDATE jobs 
       SET embedding = $1, updated_at = NOW()
       WHERE id = $2
       RETURNING *`,
      [embedding, this.id]
    );

    return new Job(result.rows[0]);
  }

  // Update job
  async update(updateData) {
    const allowedFields = [
      'title', 'company', 'description', 'requirements', 'location',
      'salary_min', 'salary_max', 'job_type', 'experience_level', 'is_active'
    ];
    
    const updates = [];
    const values = [];
    let paramCount = 1;

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    }

    if (updates.length === 0) {
      return this;
    }

    updates.push(`updated_at = NOW()`);
    values.push(this.id);

    const result = await query(
      `UPDATE jobs SET ${updates.join(', ')} WHERE id = $${paramCount} 
       RETURNING *`,
      values
    );

    return new Job(result.rows[0]);
  }

  // Get job with required skills
  async getWithSkills() {
    const skillsResult = await query(
      `SELECT s.* FROM skills s
       JOIN job_skills js ON s.id = js.skill_id
       WHERE js.job_id = $1`,
      [this.id]
    );

    return {
      ...this.toJSON(),
      skills: skillsResult.rows
    };
  }

  // Deactivate job
  async deactivate() {
    return await this.update({ is_active: false });
  }

  // Activate job
  async activate() {
    return await this.update({ is_active: true });
  }

  // Delete job
  async delete() {
    await query('DELETE FROM jobs WHERE id = $1', [this.id]);
  }

  // Get jobs by company
  static async findByCompany(company, limit = 10) {
    const result = await query(
      `SELECT * FROM jobs WHERE company ILIKE $1 AND is_active = true
       ORDER BY created_at DESC 
       LIMIT $2`,
      [`%${company}%`, limit]
    );

    return result.rows.map(row => new Job(row));
  }

  toJSON() {
    return {
      id: this.id,
      title: this.title,
      company: this.company,
      description: this.description,
      requirements: this.requirements,
      location: this.location,
      salaryMin: this.salaryMin,
      salaryMax: this.salaryMax,
      jobType: this.jobType,
      experienceLevel: this.experienceLevel,
      embedding: this.embedding,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Job;
