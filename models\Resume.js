const { query } = require('../config/database');

class Resume {
  constructor(resumeData) {
    this.id = resumeData.id;
    this.userId = resumeData.user_id;
    this.fileName = resumeData.file_name;
    this.filePath = resumeData.file_path;
    this.fileType = resumeData.file_type;
    this.rawText = resumeData.raw_text;
    this.parsedData = resumeData.parsed_data;
    this.embedding = resumeData.embedding;
    this.status = resumeData.status;
    this.createdAt = resumeData.created_at;
    this.updatedAt = resumeData.updated_at;
  }

  // Create a new resume
  static async create({ userId, fileName, filePath, fileType, rawText }) {
    const result = await query(
      `INSERT INTO resumes (user_id, file_name, file_path, file_type, raw_text, status, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, 'pending', NOW(), NOW())
       RETURNING *`,
      [userId, fileName, filePath, fileType, rawText]
    );

    return new Resume(result.rows[0]);
  }

  // Find resume by ID
  static async findById(id) {
    const result = await query(
      'SELECT * FROM resumes WHERE id = $1',
      [id]
    );

    return result.rows.length > 0 ? new Resume(result.rows[0]) : null;
  }

  // Find resumes by user ID
  static async findByUserId(userId, limit = 10, offset = 0) {
    const result = await query(
      `SELECT * FROM resumes WHERE user_id = $1 
       ORDER BY created_at DESC 
       LIMIT $2 OFFSET $3`,
      [userId, limit, offset]
    );

    return result.rows.map(row => new Resume(row));
  }

  // Update resume with parsed data
  async updateParsedData(parsedData, embedding = null) {
    const result = await query(
      `UPDATE resumes 
       SET parsed_data = $1, embedding = $2, status = 'completed', updated_at = NOW()
       WHERE id = $3
       RETURNING *`,
      [JSON.stringify(parsedData), embedding, this.id]
    );

    return new Resume(result.rows[0]);
  }

  // Update resume status
  async updateStatus(status, errorMessage = null) {
    const result = await query(
      `UPDATE resumes 
       SET status = $1, error_message = $2, updated_at = NOW()
       WHERE id = $3
       RETURNING *`,
      [status, errorMessage, this.id]
    );

    return new Resume(result.rows[0]);
  }

  // Get resume with skills
  async getWithSkills() {
    const skillsResult = await query(
      `SELECT s.* FROM skills s
       JOIN resume_skills rs ON s.id = rs.skill_id
       WHERE rs.resume_id = $1`,
      [this.id]
    );

    return {
      ...this.toJSON(),
      skills: skillsResult.rows
    };
  }

  // Get resume with experiences
  async getWithExperiences() {
    const experiencesResult = await query(
      `SELECT * FROM experiences WHERE resume_id = $1 ORDER BY start_date DESC`,
      [this.id]
    );

    return {
      ...this.toJSON(),
      experiences: experiencesResult.rows
    };
  }

  // Get resume with education
  async getWithEducation() {
    const educationResult = await query(
      `SELECT * FROM education WHERE resume_id = $1 ORDER BY start_date DESC`,
      [this.id]
    );

    return {
      ...this.toJSON(),
      education: educationResult.rows
    };
  }

  // Get complete resume data
  async getComplete() {
    const [skills, experiences, education] = await Promise.all([
      query(
        `SELECT s.* FROM skills s
         JOIN resume_skills rs ON s.id = rs.skill_id
         WHERE rs.resume_id = $1`,
        [this.id]
      ),
      query(
        `SELECT * FROM experiences WHERE resume_id = $1 ORDER BY start_date DESC`,
        [this.id]
      ),
      query(
        `SELECT * FROM education WHERE resume_id = $1 ORDER BY start_date DESC`,
        [this.id]
      )
    ]);

    return {
      ...this.toJSON(),
      skills: skills.rows,
      experiences: experiences.rows,
      education: education.rows
    };
  }

  // Delete resume
  async delete() {
    await query('DELETE FROM resumes WHERE id = $1', [this.id]);
  }

  // Search resumes by skills
  static async searchBySkills(skillNames, limit = 10) {
    const result = await query(
      `SELECT DISTINCT r.* FROM resumes r
       JOIN resume_skills rs ON r.id = rs.resume_id
       JOIN skills s ON rs.skill_id = s.id
       WHERE s.name = ANY($1) AND r.status = 'completed'
       ORDER BY r.updated_at DESC
       LIMIT $2`,
      [skillNames, limit]
    );

    return result.rows.map(row => new Resume(row));
  }

  toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      fileName: this.fileName,
      filePath: this.filePath,
      fileType: this.fileType,
      rawText: this.rawText,
      parsedData: this.parsedData ? JSON.parse(this.parsedData) : null,
      embedding: this.embedding,
      status: this.status,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Resume;
