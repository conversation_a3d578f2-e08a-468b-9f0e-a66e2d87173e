const express = require('express');
const Joi = require('joi');
const { auth, adminAuth, optionalAuth } = require('../middleware/auth');
const Job = require('../models/Job');
const jobMatchingService = require('../services/jobMatchingService');
const openaiService = require('../services/openaiService');

const router = express.Router();

// Validation schema for job creation
const jobSchema = Joi.object({
  title: Joi.string().min(2).max(255).required(),
  company: Joi.string().min(2).max(255).required(),
  description: Joi.string().min(10).required(),
  requirements: Joi.string().min(10),
  location: Joi.string().max(255),
  salaryMin: Joi.number().integer().min(0),
  salaryMax: Joi.number().integer().min(0),
  jobType: Joi.string().valid('full-time', 'part-time', 'contract', 'internship', 'remote'),
  experienceLevel: Joi.string().valid('entry', 'mid', 'senior', 'lead', 'executive')
});

// @route   GET /api/jobs
// @desc    Get all active jobs with optional filtering
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 50); // Max 50 per page
    const offset = (page - 1) * limit;

    // Extract search parameters
    const searchParams = {
      title: req.query.title,
      company: req.query.company,
      location: req.query.location,
      jobType: req.query.jobType,
      experienceLevel: req.query.experienceLevel,
      salaryMin: req.query.salaryMin ? parseInt(req.query.salaryMin) : undefined,
      salaryMax: req.query.salaryMax ? parseInt(req.query.salaryMax) : undefined,
      limit,
      offset
    };

    const jobs = await Job.search(searchParams);

    res.json({
      jobs: jobs.map(job => job.toJSON()),
      pagination: {
        page,
        limit,
        hasMore: jobs.length === limit
      },
      filters: {
        title: searchParams.title,
        company: searchParams.company,
        location: searchParams.location,
        jobType: searchParams.jobType,
        experienceLevel: searchParams.experienceLevel,
        salaryMin: searchParams.salaryMin,
        salaryMax: searchParams.salaryMax
      }
    });

  } catch (error) {
    console.error('Get jobs error:', error);
    res.status(500).json({
      error: 'Failed to fetch jobs'
    });
  }
});

// @route   GET /api/jobs/:id
// @desc    Get specific job details
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found'
      });
    }

    if (!job.isActive) {
      return res.status(404).json({
        error: 'Job is no longer active'
      });
    }

    // Get job with skills
    const jobWithSkills = await job.getWithSkills();

    res.json({
      job: jobWithSkills
    });

  } catch (error) {
    console.error('Get job error:', error);
    res.status(500).json({
      error: 'Failed to fetch job'
    });
  }
});

// @route   POST /api/jobs
// @desc    Create a new job (Admin only)
// @access  Private (Admin)
router.post('/', adminAuth, async (req, res) => {
  try {
    // Validate input
    const { error, value } = jobSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    // Create job
    const job = await Job.create(value);

    // Generate embedding for the job asynchronously
    const jobText = `${job.title}\n${job.company}\n${job.description}\n${job.requirements || ''}`;
    openaiService.generateEmbedding(jobText)
      .then(embedding => {
        return job.updateEmbedding(JSON.stringify(embedding));
      })
      .then(() => {
        console.log(`Job ${job.id} embedding generated successfully`);
      })
      .catch(error => {
        console.error(`Error generating embedding for job ${job.id}:`, error);
      });

    res.status(201).json({
      message: 'Job created successfully',
      job: job.toJSON()
    });

  } catch (error) {
    console.error('Create job error:', error);
    res.status(500).json({
      error: 'Failed to create job'
    });
  }
});

// @route   PUT /api/jobs/:id
// @desc    Update a job (Admin only)
// @access  Private (Admin)
router.put('/:id', adminAuth, async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found'
      });
    }

    // Validate input
    const updateSchema = jobSchema.fork(Object.keys(jobSchema.describe().keys), (schema) => schema.optional());
    const { error, value } = updateSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details[0].message
      });
    }

    // Update job
    const updatedJob = await job.update(value);

    // Regenerate embedding if content changed
    const contentFields = ['title', 'company', 'description', 'requirements'];
    const contentChanged = contentFields.some(field => value[field] !== undefined);
    
    if (contentChanged) {
      const jobText = `${updatedJob.title}\n${updatedJob.company}\n${updatedJob.description}\n${updatedJob.requirements || ''}`;
      openaiService.generateEmbedding(jobText)
        .then(embedding => {
          return updatedJob.updateEmbedding(JSON.stringify(embedding));
        })
        .then(() => {
          console.log(`Job ${updatedJob.id} embedding updated successfully`);
        })
        .catch(error => {
          console.error(`Error updating embedding for job ${updatedJob.id}:`, error);
        });
    }

    res.json({
      message: 'Job updated successfully',
      job: updatedJob.toJSON()
    });

  } catch (error) {
    console.error('Update job error:', error);
    res.status(500).json({
      error: 'Failed to update job'
    });
  }
});

// @route   DELETE /api/jobs/:id
// @desc    Delete a job (Admin only)
// @access  Private (Admin)
router.delete('/:id', adminAuth, async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found'
      });
    }

    await job.delete();

    res.json({
      message: 'Job deleted successfully'
    });

  } catch (error) {
    console.error('Delete job error:', error);
    res.status(500).json({
      error: 'Failed to delete job'
    });
  }
});

// @route   POST /api/jobs/:id/deactivate
// @desc    Deactivate a job (Admin only)
// @access  Private (Admin)
router.post('/:id/deactivate', adminAuth, async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found'
      });
    }

    await job.deactivate();

    res.json({
      message: 'Job deactivated successfully'
    });

  } catch (error) {
    console.error('Deactivate job error:', error);
    res.status(500).json({
      error: 'Failed to deactivate job'
    });
  }
});

// @route   POST /api/jobs/:id/activate
// @desc    Activate a job (Admin only)
// @access  Private (Admin)
router.post('/:id/activate', adminAuth, async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found'
      });
    }

    await job.activate();

    res.json({
      message: 'Job activated successfully'
    });

  } catch (error) {
    console.error('Activate job error:', error);
    res.status(500).json({
      error: 'Failed to activate job'
    });
  }
});

// @route   GET /api/jobs/:id/matches
// @desc    Get resume matches for a job (Admin only)
// @access  Private (Admin)
router.get('/:id/matches', adminAuth, async (req, res) => {
  try {
    const job = await Job.findById(req.params.id);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found'
      });
    }

    const limit = parseInt(req.query.limit) || 10;
    const matches = await jobMatchingService.findMatchingResumes(job.id, limit);

    res.json({
      jobId: job.id,
      matches,
      totalMatches: matches.length
    });

  } catch (error) {
    console.error('Get job matches error:', error);
    res.status(500).json({
      error: 'Failed to fetch resume matches'
    });
  }
});

module.exports = router;
