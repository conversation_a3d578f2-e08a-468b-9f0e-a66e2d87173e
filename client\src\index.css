@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300;
  }
  
  .btn-outline {
    @apply btn border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-sm {
    @apply h-9 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 py-2 px-4;
  }
  
  .btn-lg {
    @apply h-11 px-8;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }
  
  .card-description {
    @apply text-sm text-muted-foreground;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
  
  .badge-default {
    @apply badge border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }
  
  .badge-secondary {
    @apply badge border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .badge-outline {
    @apply badge text-foreground;
  }
  
  .progress {
    @apply relative h-4 w-full overflow-hidden rounded-full bg-secondary;
  }
  
  .progress-indicator {
    @apply h-full w-full flex-1 bg-primary transition-all;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animate-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-out {
    animation: fadeOut 0.5s ease-in-out;
  }
  
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .slide-up {
    animation: slideUp 0.3s ease-out;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-secondary-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-secondary-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-secondary-400;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
}

/* File upload styles */
.file-upload-zone {
  @apply border-2 border-dashed border-secondary-300 rounded-lg p-8 text-center transition-colors hover:border-primary-400 hover:bg-primary-50;
}

.file-upload-zone.dragover {
  @apply border-primary-500 bg-primary-100;
}

/* Match score colors */
.match-score-excellent {
  @apply text-green-600 bg-green-100;
}

.match-score-very-good {
  @apply text-blue-600 bg-blue-100;
}

.match-score-good {
  @apply text-yellow-600 bg-yellow-100;
}

.match-score-fair {
  @apply text-orange-600 bg-orange-100;
}

.match-score-poor {
  @apply text-red-600 bg-red-100;
}
