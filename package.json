{"name": "resume-parser-job-matcher", "version": "1.0.0", "description": "Automated Resume Parser + Job Matcher with AI-powered matching", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node migrations/migrate.js", "seed": "node migrations/seed.js", "test": "jest", "client": "cd client && npm run dev", "build": "cd client && npm run build", "heroku-postbuild": "cd client && npm install && npm run build"}, "keywords": ["resume-parser", "job-matcher", "ai", "nlp", "openai", "langchain"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "openai": "^4.20.1", "langchain": "^0.0.208", "@langchain/openai": "^0.0.14", "uuid": "^9.0.1", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}