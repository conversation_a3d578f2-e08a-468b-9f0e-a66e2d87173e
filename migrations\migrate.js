const { query, closePool } = require('../config/database');

const migrations = [
  // Create users table
  `CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
  )`,

  // Create resumes table
  `CREATE TABLE IF NOT EXISTS resumes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(10) NOT NULL,
    raw_text TEXT,
    parsed_data JSONB,
    embedding JSON<PERSON>,
    status VARCHAR(50) DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
  )`,

  // Create jobs table
  `CREATE TABLE IF NOT EXISTS jobs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    company VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT,
    location VARCHAR(255),
    salary_min INTEGER,
    salary_max INTEGER,
    job_type VARCHAR(50),
    experience_level VARCHAR(50),
    embedding JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
  )`,

  // Create skills table
  `CREATE TABLE IF NOT EXISTS skills (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100) DEFAULT 'Technical',
    created_at TIMESTAMP DEFAULT NOW()
  )`,

  // Create resume_skills junction table
  `CREATE TABLE IF NOT EXISTS resume_skills (
    id SERIAL PRIMARY KEY,
    resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
    skill_id INTEGER REFERENCES skills(id) ON DELETE CASCADE,
    level VARCHAR(50) DEFAULT 'Intermediate',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(resume_id, skill_id)
  )`,

  // Create job_skills junction table
  `CREATE TABLE IF NOT EXISTS job_skills (
    id SERIAL PRIMARY KEY,
    job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,
    skill_id INTEGER REFERENCES skills(id) ON DELETE CASCADE,
    required BOOLEAN DEFAULT true,
    level VARCHAR(50) DEFAULT 'Intermediate',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(job_id, skill_id)
  )`,

  // Create experiences table
  `CREATE TABLE IF NOT EXISTS experiences (
    id SERIAL PRIMARY KEY,
    resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    company VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    start_date VARCHAR(20),
    end_date VARCHAR(20),
    description TEXT,
    technologies JSONB,
    created_at TIMESTAMP DEFAULT NOW()
  )`,

  // Create education table
  `CREATE TABLE IF NOT EXISTS education (
    id SERIAL PRIMARY KEY,
    resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
    degree VARCHAR(255) NOT NULL,
    field VARCHAR(255),
    institution VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    start_date VARCHAR(20),
    end_date VARCHAR(20),
    gpa VARCHAR(10),
    created_at TIMESTAMP DEFAULT NOW()
  )`,

  // Create job_matches table
  `CREATE TABLE IF NOT EXISTS job_matches (
    id SERIAL PRIMARY KEY,
    resume_id INTEGER REFERENCES resumes(id) ON DELETE CASCADE,
    job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,
    match_score INTEGER NOT NULL,
    embedding_similarity INTEGER,
    ai_match_score INTEGER,
    analysis JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(resume_id, job_id)
  )`,

  // Create indexes for better performance
  `CREATE INDEX IF NOT EXISTS idx_resumes_user_id ON resumes(user_id)`,
  `CREATE INDEX IF NOT EXISTS idx_resumes_status ON resumes(status)`,
  `CREATE INDEX IF NOT EXISTS idx_jobs_active ON jobs(is_active)`,
  `CREATE INDEX IF NOT EXISTS idx_jobs_company ON jobs(company)`,
  `CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs(location)`,
  `CREATE INDEX IF NOT EXISTS idx_job_matches_resume_id ON job_matches(resume_id)`,
  `CREATE INDEX IF NOT EXISTS idx_job_matches_job_id ON job_matches(job_id)`,
  `CREATE INDEX IF NOT EXISTS idx_job_matches_score ON job_matches(match_score DESC)`,
  `CREATE INDEX IF NOT EXISTS idx_skills_name ON skills(name)`,
  `CREATE INDEX IF NOT EXISTS idx_resume_skills_resume_id ON resume_skills(resume_id)`,
  `CREATE INDEX IF NOT EXISTS idx_job_skills_job_id ON job_skills(job_id)`
];

async function runMigrations() {
  console.log('🔄 Running database migrations...');
  
  try {
    for (let i = 0; i < migrations.length; i++) {
      console.log(`Running migration ${i + 1}/${migrations.length}...`);
      await query(migrations[i]);
    }
    
    console.log('✅ All migrations completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await closePool();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations();
}

module.exports = { runMigrations };
