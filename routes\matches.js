const express = require('express');
const { auth } = require('../middleware/auth');
const { query } = require('../config/database');
const Resume = require('../models/Resume');
const Job = require('../models/Job');
const jobMatchingService = require('../services/jobMatchingService');

const router = express.Router();

// @route   GET /api/matches/resume/:resumeId
// @desc    Get job matches for a specific resume
// @access  Private
router.get('/resume/:resumeId', auth, async (req, res) => {
  try {
    const resume = await Resume.findById(req.params.resumeId);

    if (!resume) {
      return res.status(404).json({
        error: 'Resume not found'
      });
    }

    // Check if user owns this resume
    if (resume.userId !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    if (resume.status !== 'completed') {
      return res.status(400).json({
        error: 'Resume not processed yet',
        message: 'Please wait for resume processing to complete'
      });
    }

    const limit = parseInt(req.query.limit) || 10;
    const refresh = req.query.refresh === 'true';

    let matches;
    if (refresh) {
      // Generate fresh matches
      matches = await jobMatchingService.findMatchingJobs(resume.id, limit);
    } else {
      // Get stored matches first, generate if none exist
      matches = await jobMatchingService.getStoredMatches(resume.id, limit);
      if (matches.length === 0) {
        matches = await jobMatchingService.findMatchingJobs(resume.id, limit);
      }
    }

    res.json({
      resumeId: resume.id,
      matches,
      totalMatches: matches.length,
      refreshed: refresh
    });

  } catch (error) {
    console.error('Get resume matches error:', error);
    res.status(500).json({
      error: 'Failed to fetch job matches'
    });
  }
});

// @route   GET /api/matches/user
// @desc    Get all matches for the current user's resumes
// @access  Private
router.get('/user', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 50);
    const offset = (page - 1) * limit;

    // Get all user's completed resumes
    const userResumes = await Resume.findByUserId(req.user.id);
    const completedResumeIds = userResumes
      .filter(resume => resume.status === 'completed')
      .map(resume => resume.id);

    if (completedResumeIds.length === 0) {
      return res.json({
        matches: [],
        pagination: { page, limit, hasMore: false },
        message: 'No processed resumes found'
      });
    }

    // Get matches for all user's resumes
    const result = await query(
      `SELECT 
         jm.*,
         r.file_name as resume_file_name,
         j.title, j.company, j.location, j.job_type, j.experience_level,
         j.salary_min, j.salary_max, j.description
       FROM job_matches jm
       JOIN resumes r ON jm.resume_id = r.id
       JOIN jobs j ON jm.job_id = j.id
       WHERE jm.resume_id = ANY($1) AND j.is_active = true
       ORDER BY jm.match_score DESC, jm.created_at DESC
       LIMIT $2 OFFSET $3`,
      [completedResumeIds, limit, offset]
    );

    const matches = result.rows.map(row => ({
      id: row.id,
      resumeId: row.resume_id,
      resumeFileName: row.resume_file_name,
      job: {
        id: row.job_id,
        title: row.title,
        company: row.company,
        location: row.location,
        jobType: row.job_type,
        experienceLevel: row.experience_level,
        salaryMin: row.salary_min,
        salaryMax: row.salary_max,
        description: row.description
      },
      matchScore: row.match_score,
      embeddingSimilarity: row.embedding_similarity,
      aiMatchScore: row.ai_match_score,
      analysis: JSON.parse(row.analysis),
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    res.json({
      matches,
      pagination: {
        page,
        limit,
        hasMore: matches.length === limit
      }
    });

  } catch (error) {
    console.error('Get user matches error:', error);
    res.status(500).json({
      error: 'Failed to fetch user matches'
    });
  }
});

// @route   GET /api/matches/stats
// @desc    Get matching statistics for the user
// @access  Private
router.get('/stats', auth, async (req, res) => {
  try {
    // Get user's resume count by status
    const resumeStats = await query(
      `SELECT status, COUNT(*) as count
       FROM resumes 
       WHERE user_id = $1 
       GROUP BY status`,
      [req.user.id]
    );

    // Get total matches for user's resumes
    const matchStats = await query(
      `SELECT 
         COUNT(*) as total_matches,
         AVG(match_score) as avg_match_score,
         MAX(match_score) as best_match_score
       FROM job_matches jm
       JOIN resumes r ON jm.resume_id = r.id
       WHERE r.user_id = $1`,
      [req.user.id]
    );

    // Get top matching companies
    const topCompanies = await query(
      `SELECT 
         j.company,
         COUNT(*) as match_count,
         AVG(jm.match_score) as avg_score
       FROM job_matches jm
       JOIN resumes r ON jm.resume_id = r.id
       JOIN jobs j ON jm.job_id = j.id
       WHERE r.user_id = $1
       GROUP BY j.company
       ORDER BY avg_score DESC, match_count DESC
       LIMIT 5`,
      [req.user.id]
    );

    // Get match score distribution
    const scoreDistribution = await query(
      `SELECT 
         CASE 
           WHEN match_score >= 90 THEN 'Excellent (90-100)'
           WHEN match_score >= 80 THEN 'Very Good (80-89)'
           WHEN match_score >= 70 THEN 'Good (70-79)'
           WHEN match_score >= 60 THEN 'Fair (60-69)'
           ELSE 'Poor (0-59)'
         END as score_range,
         COUNT(*) as count
       FROM job_matches jm
       JOIN resumes r ON jm.resume_id = r.id
       WHERE r.user_id = $1
       GROUP BY score_range
       ORDER BY MIN(match_score) DESC`,
      [req.user.id]
    );

    res.json({
      resumeStats: resumeStats.rows.reduce((acc, row) => {
        acc[row.status] = parseInt(row.count);
        return acc;
      }, {}),
      matchStats: {
        totalMatches: parseInt(matchStats.rows[0]?.total_matches || 0),
        avgMatchScore: parseFloat(matchStats.rows[0]?.avg_match_score || 0).toFixed(1),
        bestMatchScore: parseInt(matchStats.rows[0]?.best_match_score || 0)
      },
      topCompanies: topCompanies.rows.map(row => ({
        company: row.company,
        matchCount: parseInt(row.match_count),
        avgScore: parseFloat(row.avg_score).toFixed(1)
      })),
      scoreDistribution: scoreDistribution.rows.map(row => ({
        range: row.score_range,
        count: parseInt(row.count)
      }))
    });

  } catch (error) {
    console.error('Get match stats error:', error);
    res.status(500).json({
      error: 'Failed to fetch match statistics'
    });
  }
});

// @route   POST /api/matches/refresh
// @desc    Refresh matches for all user's resumes
// @access  Private
router.post('/refresh', auth, async (req, res) => {
  try {
    // Get all user's completed resumes
    const userResumes = await Resume.findByUserId(req.user.id);
    const completedResumes = userResumes.filter(resume => resume.status === 'completed');

    if (completedResumes.length === 0) {
      return res.status(400).json({
        error: 'No processed resumes found',
        message: 'Please upload and process at least one resume first'
      });
    }

    const limit = parseInt(req.query.limit) || 10;
    let totalMatches = 0;

    // Refresh matches for each resume
    const refreshPromises = completedResumes.map(async (resume) => {
      try {
        const matches = await jobMatchingService.findMatchingJobs(resume.id, limit);
        totalMatches += matches.length;
        return {
          resumeId: resume.id,
          fileName: resume.fileName,
          matchCount: matches.length
        };
      } catch (error) {
        console.error(`Error refreshing matches for resume ${resume.id}:`, error);
        return {
          resumeId: resume.id,
          fileName: resume.fileName,
          matchCount: 0,
          error: error.message
        };
      }
    });

    const results = await Promise.all(refreshPromises);

    res.json({
      message: 'Matches refreshed successfully',
      results,
      totalMatches,
      processedResumes: completedResumes.length
    });

  } catch (error) {
    console.error('Refresh matches error:', error);
    res.status(500).json({
      error: 'Failed to refresh matches'
    });
  }
});

// @route   DELETE /api/matches/resume/:resumeId
// @desc    Delete all matches for a specific resume
// @access  Private
router.delete('/resume/:resumeId', auth, async (req, res) => {
  try {
    const resume = await Resume.findById(req.params.resumeId);

    if (!resume) {
      return res.status(404).json({
        error: 'Resume not found'
      });
    }

    // Check if user owns this resume
    if (resume.userId !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Delete all matches for this resume
    const result = await query(
      'DELETE FROM job_matches WHERE resume_id = $1',
      [resume.id]
    );

    res.json({
      message: 'Matches deleted successfully',
      deletedCount: result.rowCount
    });

  } catch (error) {
    console.error('Delete matches error:', error);
    res.status(500).json({
      error: 'Failed to delete matches'
    });
  }
});

module.exports = router;
