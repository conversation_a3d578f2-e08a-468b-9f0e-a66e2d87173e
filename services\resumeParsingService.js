const fs = require('fs').promises;
const path = require('path');
const pdfParse = require('pdf-parse');
const mammoth = require('mammoth');
const openaiService = require('./openaiService');
const { query } = require('../config/database');

class ResumeParsingService {
  constructor() {
    this.supportedFormats = ['.pdf', '.doc', '.docx'];
  }

  // Extract text from uploaded file
  async extractTextFromFile(filePath, fileType) {
    try {
      const fileBuffer = await fs.readFile(filePath);
      
      switch (fileType.toLowerCase()) {
        case '.pdf':
          return await this.extractFromPDF(fileBuffer);
        case '.doc':
        case '.docx':
          return await this.extractFromWord(fileBuffer);
        default:
          throw new Error(`Unsupported file format: ${fileType}`);
      }
    } catch (error) {
      console.error('Error extracting text from file:', error);
      throw new Error(`Failed to extract text: ${error.message}`);
    }
  }

  // Extract text from PDF
  async extractFromPDF(buffer) {
    try {
      const data = await pdfParse(buffer);
      return data.text;
    } catch (error) {
      throw new Error(`PDF parsing failed: ${error.message}`);
    }
  }

  // Extract text from Word document
  async extractFromWord(buffer) {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } catch (error) {
      throw new Error(`Word document parsing failed: ${error.message}`);
    }
  }

  // Parse resume and extract structured data
  async parseResume(resumeId, filePath, fileType) {
    try {
      // Extract raw text
      const rawText = await this.extractTextFromFile(filePath, fileType);
      
      if (!rawText || rawText.trim().length === 0) {
        throw new Error('No text content found in the file');
      }

      // Update resume with raw text
      await query(
        'UPDATE resumes SET raw_text = $1, status = $2 WHERE id = $3',
        [rawText, 'processing', resumeId]
      );

      // Extract structured data using OpenAI
      const parsedData = await openaiService.extractResumeEntities(rawText);
      
      // Generate embedding for the resume
      const resumeEmbedding = await openaiService.generateEmbedding(rawText);

      // Save parsed data and embedding
      await query(
        `UPDATE resumes 
         SET parsed_data = $1, embedding = $2, status = $3, updated_at = NOW()
         WHERE id = $4`,
        [JSON.stringify(parsedData), JSON.stringify(resumeEmbedding), 'completed', resumeId]
      );

      // Store skills in database
      if (parsedData.skills && parsedData.skills.length > 0) {
        await this.storeSkills(resumeId, parsedData.skills);
      }

      // Store experience in database
      if (parsedData.experience && parsedData.experience.length > 0) {
        await this.storeExperience(resumeId, parsedData.experience);
      }

      // Store education in database
      if (parsedData.education && parsedData.education.length > 0) {
        await this.storeEducation(resumeId, parsedData.education);
      }

      return {
        resumeId,
        rawText,
        parsedData,
        status: 'completed'
      };

    } catch (error) {
      console.error('Error parsing resume:', error);
      
      // Update resume status to failed
      await query(
        'UPDATE resumes SET status = $1, error_message = $2 WHERE id = $3',
        ['failed', error.message, resumeId]
      );
      
      throw error;
    }
  }

  // Store skills in database
  async storeSkills(resumeId, skills) {
    try {
      for (const skill of skills) {
        // Insert or get skill
        const skillResult = await query(
          `INSERT INTO skills (name, category) 
           VALUES ($1, $2) 
           ON CONFLICT (name) DO UPDATE SET category = $2
           RETURNING id`,
          [skill.name, skill.category || 'Technical']
        );

        const skillId = skillResult.rows[0].id;

        // Link skill to resume
        await query(
          `INSERT INTO resume_skills (resume_id, skill_id, level) 
           VALUES ($1, $2, $3) 
           ON CONFLICT (resume_id, skill_id) DO UPDATE SET level = $3`,
          [resumeId, skillId, skill.level || 'Intermediate']
        );
      }
    } catch (error) {
      console.error('Error storing skills:', error);
      throw error;
    }
  }

  // Store experience in database
  async storeExperience(resumeId, experiences) {
    try {
      for (const exp of experiences) {
        await query(
          `INSERT INTO experiences (
            resume_id, title, company, location, start_date, end_date, 
            description, technologies
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            resumeId,
            exp.title,
            exp.company,
            exp.location,
            exp.startDate,
            exp.endDate === 'Present' ? null : exp.endDate,
            exp.description,
            JSON.stringify(exp.technologies || [])
          ]
        );
      }
    } catch (error) {
      console.error('Error storing experience:', error);
      throw error;
    }
  }

  // Store education in database
  async storeEducation(resumeId, educations) {
    try {
      for (const edu of educations) {
        await query(
          `INSERT INTO education (
            resume_id, degree, field, institution, location, 
            start_date, end_date, gpa
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            resumeId,
            edu.degree,
            edu.field,
            edu.institution,
            edu.location,
            edu.startDate,
            edu.endDate,
            edu.gpa
          ]
        );
      }
    } catch (error) {
      console.error('Error storing education:', error);
      throw error;
    }
  }

  // Validate file format
  isValidFormat(fileType) {
    return this.supportedFormats.includes(fileType.toLowerCase());
  }

  // Get file extension from filename
  getFileExtension(filename) {
    return path.extname(filename).toLowerCase();
  }

  // Clean up uploaded file
  async cleanupFile(filePath) {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.error('Error cleaning up file:', error);
    }
  }
}

module.exports = new ResumeParsingService();
