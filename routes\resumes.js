const express = require('express');
const { auth } = require('../middleware/auth');
const { uploadSingle, cleanupFiles } = require('../middleware/upload');
const Resume = require('../models/Resume');
const resumeParsingService = require('../services/resumeParsingService');
const jobMatchingService = require('../services/jobMatchingService');

const router = express.Router();

// @route   POST /api/resumes/upload
// @desc    Upload and parse resume
// @access  Private
router.post('/upload', auth, uploadSingle('resume'), async (req, res) => {
  try {
    const { originalName, filename, path: filePath, extension } = req.fileInfo;

    // Validate file format
    if (!resumeParsingService.isValidFormat(extension)) {
      cleanupFiles([filePath]);
      return res.status(400).json({
        error: 'Invalid file format',
        message: 'Supported formats: PDF, DOC, DOCX'
      });
    }

    // Create resume record
    const resume = await Resume.create({
      userId: req.user.id,
      fileName: originalName,
      filePath: filePath,
      fileType: extension,
      rawText: null
    });

    // Start parsing process asynchronously
    resumeParsingService.parseResume(resume.id, filePath, extension)
      .then(() => {
        console.log(`Resume ${resume.id} parsed successfully`);
      })
      .catch((error) => {
        console.error(`Error parsing resume ${resume.id}:`, error);
      });

    res.status(201).json({
      message: 'Resume uploaded successfully. Processing started.',
      resume: {
        id: resume.id,
        fileName: resume.fileName,
        status: resume.status,
        createdAt: resume.createdAt
      }
    });

  } catch (error) {
    // Cleanup uploaded file on error
    if (req.fileInfo) {
      cleanupFiles([req.fileInfo.path]);
    }
    
    console.error('Resume upload error:', error);
    res.status(500).json({
      error: 'Upload failed',
      message: error.message
    });
  }
});

// @route   GET /api/resumes
// @desc    Get user's resumes
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const resumes = await Resume.findByUserId(req.user.id, limit, offset);

    res.json({
      resumes: resumes.map(resume => resume.toJSON()),
      pagination: {
        page,
        limit,
        hasMore: resumes.length === limit
      }
    });

  } catch (error) {
    console.error('Get resumes error:', error);
    res.status(500).json({
      error: 'Failed to fetch resumes'
    });
  }
});

// @route   GET /api/resumes/:id
// @desc    Get specific resume with details
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const resume = await Resume.findById(req.params.id);

    if (!resume) {
      return res.status(404).json({
        error: 'Resume not found'
      });
    }

    // Check if user owns this resume
    if (resume.userId !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Get complete resume data with skills, experience, and education
    const completeResume = await resume.getComplete();

    res.json({
      resume: completeResume
    });

  } catch (error) {
    console.error('Get resume error:', error);
    res.status(500).json({
      error: 'Failed to fetch resume'
    });
  }
});

// @route   GET /api/resumes/:id/status
// @desc    Get resume processing status
// @access  Private
router.get('/:id/status', auth, async (req, res) => {
  try {
    const resume = await Resume.findById(req.params.id);

    if (!resume) {
      return res.status(404).json({
        error: 'Resume not found'
      });
    }

    // Check if user owns this resume
    if (resume.userId !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    res.json({
      id: resume.id,
      status: resume.status,
      fileName: resume.fileName,
      createdAt: resume.createdAt,
      updatedAt: resume.updatedAt
    });

  } catch (error) {
    console.error('Get resume status error:', error);
    res.status(500).json({
      error: 'Failed to fetch resume status'
    });
  }
});

// @route   POST /api/resumes/:id/reprocess
// @desc    Reprocess a resume
// @access  Private
router.post('/:id/reprocess', auth, async (req, res) => {
  try {
    const resume = await Resume.findById(req.params.id);

    if (!resume) {
      return res.status(404).json({
        error: 'Resume not found'
      });
    }

    // Check if user owns this resume
    if (resume.userId !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Update status to pending
    await resume.updateStatus('pending');

    // Start reprocessing
    resumeParsingService.parseResume(resume.id, resume.filePath, resume.fileType)
      .then(() => {
        console.log(`Resume ${resume.id} reprocessed successfully`);
      })
      .catch((error) => {
        console.error(`Error reprocessing resume ${resume.id}:`, error);
      });

    res.json({
      message: 'Resume reprocessing started',
      status: 'pending'
    });

  } catch (error) {
    console.error('Reprocess resume error:', error);
    res.status(500).json({
      error: 'Failed to reprocess resume'
    });
  }
});

// @route   GET /api/resumes/:id/matches
// @desc    Get job matches for a resume
// @access  Private
router.get('/:id/matches', auth, async (req, res) => {
  try {
    const resume = await Resume.findById(req.params.id);

    if (!resume) {
      return res.status(404).json({
        error: 'Resume not found'
      });
    }

    // Check if user owns this resume
    if (resume.userId !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    if (resume.status !== 'completed') {
      return res.status(400).json({
        error: 'Resume not processed yet',
        message: 'Please wait for resume processing to complete'
      });
    }

    const limit = parseInt(req.query.limit) || 10;
    const refresh = req.query.refresh === 'true';

    let matches;
    if (refresh) {
      // Generate fresh matches
      matches = await jobMatchingService.findMatchingJobs(resume.id, limit);
    } else {
      // Get stored matches first, generate if none exist
      matches = await jobMatchingService.getStoredMatches(resume.id, limit);
      if (matches.length === 0) {
        matches = await jobMatchingService.findMatchingJobs(resume.id, limit);
      }
    }

    res.json({
      resumeId: resume.id,
      matches,
      totalMatches: matches.length
    });

  } catch (error) {
    console.error('Get resume matches error:', error);
    res.status(500).json({
      error: 'Failed to fetch job matches'
    });
  }
});

// @route   DELETE /api/resumes/:id
// @desc    Delete a resume
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const resume = await Resume.findById(req.params.id);

    if (!resume) {
      return res.status(404).json({
        error: 'Resume not found'
      });
    }

    // Check if user owns this resume
    if (resume.userId !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied'
      });
    }

    // Delete file from filesystem
    cleanupFiles([resume.filePath]);

    // Delete from database
    await resume.delete();

    res.json({
      message: 'Resume deleted successfully'
    });

  } catch (error) {
    console.error('Delete resume error:', error);
    res.status(500).json({
      error: 'Failed to delete resume'
    });
  }
});

module.exports = router;
