import React from 'react'
import { Upload } from 'lucide-react'

const ResumeUpload = () => {
  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <h2 className="mt-2 text-lg font-medium text-gray-900">Upload Resume</h2>
          <p className="mt-1 text-sm text-gray-600">
            Upload your resume to get started with AI-powered job matching
          </p>
        </div>
        
        <div className="mt-6">
          <div className="file-upload-zone">
            <p className="text-gray-600">Drag and drop your resume here, or click to browse</p>
            <p className="text-sm text-gray-500 mt-2">Supports PDF, DOC, DOCX files up to 10MB</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ResumeUpload
