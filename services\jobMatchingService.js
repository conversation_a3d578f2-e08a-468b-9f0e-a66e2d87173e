const openaiService = require('./openaiService');
const { query } = require('../config/database');

class JobMatchingService {
  constructor() {
    this.matchThreshold = 0.7; // Minimum similarity score for a match
  }

  // Calculate cosine similarity between two vectors
  cosineSimilarity(vecA, vecB) {
    if (vecA.length !== vecB.length) {
      throw new Error('Vectors must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (normA * normB);
  }

  // Find matching jobs for a resume
  async findMatchingJobs(resumeId, limit = 10) {
    try {
      // Get resume data
      const resumeResult = await query(
        'SELECT * FROM resumes WHERE id = $1 AND status = $2',
        [resumeId, 'completed']
      );

      if (resumeResult.rows.length === 0) {
        throw new Error('Resume not found or not processed');
      }

      const resume = resumeResult.rows[0];
      const resumeEmbedding = JSON.parse(resume.embedding);
      const resumeData = JSON.parse(resume.parsed_data);

      // Get all active jobs with embeddings
      const jobsResult = await query(
        'SELECT * FROM jobs WHERE is_active = true AND embedding IS NOT NULL'
      );

      const matches = [];

      for (const job of jobsResult.rows) {
        try {
          const jobEmbedding = JSON.parse(job.embedding);
          
          // Calculate embedding similarity
          const embeddingSimilarity = this.cosineSimilarity(resumeEmbedding, jobEmbedding);
          
          // Get detailed AI analysis
          const aiAnalysis = await openaiService.calculateMatchScore(
            resume.raw_text,
            `${job.title}\n${job.company}\n${job.description}\n${job.requirements}`
          );

          // Calculate combined score
          const combinedScore = (embeddingSimilarity * 0.4) + (aiAnalysis.matchScore * 0.6 / 100);

          if (combinedScore >= this.matchThreshold) {
            matches.push({
              job: {
                id: job.id,
                title: job.title,
                company: job.company,
                location: job.location,
                jobType: job.job_type,
                experienceLevel: job.experience_level,
                salaryMin: job.salary_min,
                salaryMax: job.salary_max
              },
              matchScore: Math.round(combinedScore * 100),
              embeddingSimilarity: Math.round(embeddingSimilarity * 100),
              aiMatchScore: aiAnalysis.matchScore,
              analysis: aiAnalysis.analysis,
              createdAt: new Date()
            });
          }
        } catch (error) {
          console.error(`Error processing job ${job.id}:`, error);
          continue;
        }
      }

      // Sort by match score
      matches.sort((a, b) => b.matchScore - a.matchScore);

      // Store matches in database
      await this.storeMatches(resumeId, matches.slice(0, limit));

      return matches.slice(0, limit);

    } catch (error) {
      console.error('Error finding matching jobs:', error);
      throw error;
    }
  }

  // Find matching resumes for a job
  async findMatchingResumes(jobId, limit = 10) {
    try {
      // Get job data
      const jobResult = await query(
        'SELECT * FROM jobs WHERE id = $1 AND is_active = true',
        [jobId]
      );

      if (jobResult.rows.length === 0) {
        throw new Error('Job not found or not active');
      }

      const job = jobResult.rows[0];
      
      // Generate job embedding if not exists
      let jobEmbedding;
      if (job.embedding) {
        jobEmbedding = JSON.parse(job.embedding);
      } else {
        const jobText = `${job.title}\n${job.company}\n${job.description}\n${job.requirements}`;
        jobEmbedding = await openaiService.generateEmbedding(jobText);
        
        // Update job with embedding
        await query(
          'UPDATE jobs SET embedding = $1 WHERE id = $2',
          [JSON.stringify(jobEmbedding), jobId]
        );
      }

      // Get all completed resumes with embeddings
      const resumesResult = await query(
        'SELECT * FROM resumes WHERE status = $1 AND embedding IS NOT NULL',
        ['completed']
      );

      const matches = [];

      for (const resume of resumesResult.rows) {
        try {
          const resumeEmbedding = JSON.parse(resume.embedding);
          
          // Calculate embedding similarity
          const embeddingSimilarity = this.cosineSimilarity(jobEmbedding, resumeEmbedding);
          
          // Get detailed AI analysis
          const aiAnalysis = await openaiService.calculateMatchScore(
            resume.raw_text,
            `${job.title}\n${job.company}\n${job.description}\n${job.requirements}`
          );

          // Calculate combined score
          const combinedScore = (embeddingSimilarity * 0.4) + (aiAnalysis.matchScore * 0.6 / 100);

          if (combinedScore >= this.matchThreshold) {
            const resumeData = JSON.parse(resume.parsed_data);
            
            matches.push({
              resume: {
                id: resume.id,
                userId: resume.user_id,
                fileName: resume.file_name,
                personalInfo: resumeData.personalInfo,
                summary: resumeData.summary
              },
              matchScore: Math.round(combinedScore * 100),
              embeddingSimilarity: Math.round(embeddingSimilarity * 100),
              aiMatchScore: aiAnalysis.matchScore,
              analysis: aiAnalysis.analysis,
              createdAt: new Date()
            });
          }
        } catch (error) {
          console.error(`Error processing resume ${resume.id}:`, error);
          continue;
        }
      }

      // Sort by match score
      matches.sort((a, b) => b.matchScore - a.matchScore);

      return matches.slice(0, limit);

    } catch (error) {
      console.error('Error finding matching resumes:', error);
      throw error;
    }
  }

  // Store job matches in database
  async storeMatches(resumeId, matches) {
    try {
      for (const match of matches) {
        await query(
          `INSERT INTO job_matches (
            resume_id, job_id, match_score, embedding_similarity, 
            ai_match_score, analysis, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
           ON CONFLICT (resume_id, job_id) 
           DO UPDATE SET 
             match_score = $3, 
             embedding_similarity = $4,
             ai_match_score = $5,
             analysis = $6,
             updated_at = NOW()`,
          [
            resumeId,
            match.job.id,
            match.matchScore,
            match.embeddingSimilarity,
            match.aiMatchScore,
            JSON.stringify(match.analysis)
          ]
        );
      }
    } catch (error) {
      console.error('Error storing matches:', error);
      throw error;
    }
  }

  // Get stored matches for a resume
  async getStoredMatches(resumeId, limit = 10) {
    try {
      const result = await query(
        `SELECT 
           jm.*,
           j.title, j.company, j.location, j.job_type, j.experience_level,
           j.salary_min, j.salary_max, j.description
         FROM job_matches jm
         JOIN jobs j ON jm.job_id = j.id
         WHERE jm.resume_id = $1 AND j.is_active = true
         ORDER BY jm.match_score DESC
         LIMIT $2`,
        [resumeId, limit]
      );

      return result.rows.map(row => ({
        job: {
          id: row.job_id,
          title: row.title,
          company: row.company,
          location: row.location,
          jobType: row.job_type,
          experienceLevel: row.experience_level,
          salaryMin: row.salary_min,
          salaryMax: row.salary_max,
          description: row.description
        },
        matchScore: row.match_score,
        embeddingSimilarity: row.embedding_similarity,
        aiMatchScore: row.ai_match_score,
        analysis: JSON.parse(row.analysis),
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } catch (error) {
      console.error('Error getting stored matches:', error);
      throw error;
    }
  }

  // Update match threshold
  setMatchThreshold(threshold) {
    if (threshold >= 0 && threshold <= 1) {
      this.matchThreshold = threshold;
    } else {
      throw new Error('Match threshold must be between 0 and 1');
    }
  }
}

module.exports = new JobMatchingService();
