import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { useQuery } from 'react-query'
import { resumeAPI, matchAPI } from '../services/api'
import { 
  Upload, 
  FileText, 
  Target, 
  TrendingUp, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Plus
} from 'lucide-react'
import LoadingSpinner, { InlineLoader } from '../components/LoadingSpinner'

const Dashboard = () => {
  // Fetch user's resumes
  const { data: resumesData, isLoading: resumesLoading } = useQuery(
    'user-resumes',
    () => resumeAPI.getAll({ limit: 5 }),
    {
      select: (response) => response.data
    }
  )

  // Fetch match statistics
  const { data: statsData, isLoading: statsLoading } = useQuery(
    'match-stats',
    () => matchAPI.getStats(),
    {
      select: (response) => response.data
    }
  )

  // Fetch recent matches
  const { data: matchesData, isLoading: matchesLoading } = useQuery(
    'user-matches',
    () => matchAPI.getUserMatches({ limit: 5 }),
    {
      select: (response) => response.data
    }
  )

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'processing':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getMatchScoreColor = (score) => {
    if (score >= 90) return 'text-green-600 bg-green-100'
    if (score >= 80) return 'text-blue-600 bg-blue-100'
    if (score >= 70) return 'text-yellow-600 bg-yellow-100'
    if (score >= 60) return 'text-orange-600 bg-orange-100'
    return 'text-red-600 bg-red-100'
  }

  const stats = [
    {
      name: 'Total Resumes',
      value: resumesData?.resumes?.length || 0,
      icon: FileText,
      color: 'bg-blue-500'
    },
    {
      name: 'Total Matches',
      value: statsData?.matchStats?.totalMatches || 0,
      icon: Target,
      color: 'bg-green-500'
    },
    {
      name: 'Avg Match Score',
      value: statsData?.matchStats?.avgMatchScore || '0.0',
      icon: TrendingUp,
      color: 'bg-purple-500'
    },
    {
      name: 'Best Match',
      value: statsData?.matchStats?.bestMatchScore || 0,
      icon: TrendingUp,
      color: 'bg-yellow-500'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome to your Dashboard
        </h1>
        <p className="text-gray-600 mb-4">
          Upload your resume and discover matching job opportunities powered by AI.
        </p>
        <Link 
          to="/upload" 
          className="btn btn-primary inline-flex items-center"
        >
          <Upload className="mr-2 h-4 w-4" />
          Upload Resume
        </Link>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.color}`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Resumes */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Recent Resumes</h2>
              <Link 
                to="/upload" 
                className="btn btn-outline btn-sm inline-flex items-center"
              >
                <Plus className="mr-1 h-4 w-4" />
                Add New
              </Link>
            </div>
          </div>
          <div className="p-6">
            {resumesLoading ? (
              <InlineLoader message="Loading resumes..." />
            ) : resumesData?.resumes?.length > 0 ? (
              <div className="space-y-4">
                {resumesData.resumes.map((resume) => (
                  <div key={resume.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center">
                      {getStatusIcon(resume.status)}
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          {resume.fileName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(resume.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        resume.status === 'completed' ? 'bg-green-100 text-green-800' :
                        resume.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                        resume.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {resume.status}
                      </span>
                      {resume.status === 'completed' && (
                        <Link 
                          to={`/resumes/${resume.id}`}
                          className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                        >
                          View
                        </Link>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No resumes</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by uploading your first resume.
                </p>
                <div className="mt-6">
                  <Link 
                    to="/upload" 
                    className="btn btn-primary btn-sm"
                  >
                    Upload Resume
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Recent Matches */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Recent Matches</h2>
              <Link 
                to="/matches" 
                className="text-primary-600 hover:text-primary-700 text-sm font-medium"
              >
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {matchesLoading ? (
              <InlineLoader message="Loading matches..." />
            ) : matchesData?.matches?.length > 0 ? (
              <div className="space-y-4">
                {matchesData.matches.map((match) => (
                  <div key={match.id} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-gray-900">
                          {match.job.title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {match.job.company} • {match.job.location}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Resume: {match.resumeFileName}
                        </p>
                      </div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getMatchScoreColor(match.matchScore)}`}>
                        {match.matchScore}% match
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Target className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No matches yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Upload a resume to start finding job matches.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link 
            to="/upload" 
            className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <Upload className="h-8 w-8 text-primary-600 mb-2" />
            <h3 className="font-medium text-gray-900">Upload Resume</h3>
            <p className="text-sm text-gray-600">Add a new resume for analysis</p>
          </Link>
          
          <Link 
            to="/matches" 
            className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <Target className="h-8 w-8 text-primary-600 mb-2" />
            <h3 className="font-medium text-gray-900">View Matches</h3>
            <p className="text-sm text-gray-600">See all job recommendations</p>
          </Link>
          
          <Link 
            to="/jobs" 
            className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <FileText className="h-8 w-8 text-primary-600 mb-2" />
            <h3 className="font-medium text-gray-900">Browse Jobs</h3>
            <p className="text-sm text-gray-600">Explore available positions</p>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
