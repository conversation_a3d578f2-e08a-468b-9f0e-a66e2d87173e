const OpenAI = require('openai');

class OpenAIService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.model = process.env.OPENAI_MODEL || 'gpt-3.5-turbo';
    this.embeddingModel = process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-ada-002';
  }

  // Extract entities from resume text using OpenAI
  async extractResumeEntities(resumeText) {
    try {
      const prompt = `
        Extract the following information from this resume text and return it as a JSON object:
        
        {
          "personalInfo": {
            "name": "Full name",
            "email": "Email address",
            "phone": "Phone number",
            "location": "City, State/Country",
            "linkedin": "LinkedIn URL",
            "github": "GitHub URL",
            "website": "Personal website URL"
          },
          "summary": "Professional summary or objective",
          "skills": [
            {
              "name": "Skill name",
              "category": "Technical/Soft/Language",
              "level": "Beginner/Intermediate/Advanced/Expert"
            }
          ],
          "experience": [
            {
              "title": "Job title",
              "company": "Company name",
              "location": "City, State",
              "startDate": "YYYY-MM",
              "endDate": "YYYY-MM or Present",
              "description": "Job description and achievements",
              "technologies": ["Tech1", "Tech2"]
            }
          ],
          "education": [
            {
              "degree": "Degree type",
              "field": "Field of study",
              "institution": "School/University name",
              "location": "City, State",
              "startDate": "YYYY-MM",
              "endDate": "YYYY-MM",
              "gpa": "GPA if mentioned"
            }
          ],
          "certifications": [
            {
              "name": "Certification name",
              "issuer": "Issuing organization",
              "date": "YYYY-MM",
              "expiryDate": "YYYY-MM if applicable"
            }
          ],
          "projects": [
            {
              "name": "Project name",
              "description": "Project description",
              "technologies": ["Tech1", "Tech2"],
              "url": "Project URL if available"
            }
          ]
        }
        
        Resume text:
        ${resumeText}
        
        Return only the JSON object, no additional text.
      `;

      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert resume parser. Extract information accurately and return only valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000
      });

      const content = response.choices[0].message.content.trim();
      
      // Try to parse the JSON response
      try {
        return JSON.parse(content);
      } catch (parseError) {
        // If JSON parsing fails, try to extract JSON from the response
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
        throw new Error('Failed to parse JSON response from OpenAI');
      }
    } catch (error) {
      console.error('Error extracting resume entities:', error);
      throw new Error(`Failed to extract resume entities: ${error.message}`);
    }
  }

  // Generate embeddings for text
  async generateEmbedding(text) {
    try {
      const response = await this.client.embeddings.create({
        model: this.embeddingModel,
        input: text,
      });

      return response.data[0].embedding;
    } catch (error) {
      console.error('Error generating embedding:', error);
      throw new Error(`Failed to generate embedding: ${error.message}`);
    }
  }

  // Calculate similarity score between resume and job
  async calculateMatchScore(resumeText, jobDescription) {
    try {
      const prompt = `
        Analyze the compatibility between this resume and job description. 
        Return a JSON object with a match score (0-100) and detailed analysis.
        
        {
          "matchScore": 85,
          "analysis": {
            "skillsMatch": {
              "score": 90,
              "matchedSkills": ["JavaScript", "React", "Node.js"],
              "missingSkills": ["Python", "AWS"]
            },
            "experienceMatch": {
              "score": 80,
              "relevantExperience": "5 years in web development",
              "experienceGap": "Limited backend experience"
            },
            "educationMatch": {
              "score": 85,
              "relevantEducation": "Computer Science degree",
              "educationGap": "No specific certifications mentioned"
            },
            "overallFit": "Strong candidate with relevant skills and experience",
            "recommendations": [
              "Highlight React experience in interview",
              "Discuss willingness to learn Python"
            ]
          }
        }
        
        Resume:
        ${resumeText}
        
        Job Description:
        ${jobDescription}
        
        Return only the JSON object.
      `;

      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert HR analyst. Provide accurate and detailed job-candidate matching analysis.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 1500
      });

      const content = response.choices[0].message.content.trim();
      
      try {
        return JSON.parse(content);
      } catch (parseError) {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
        throw new Error('Failed to parse match score response');
      }
    } catch (error) {
      console.error('Error calculating match score:', error);
      throw new Error(`Failed to calculate match score: ${error.message}`);
    }
  }

  // Extract skills from job description
  async extractJobSkills(jobDescription) {
    try {
      const prompt = `
        Extract required and preferred skills from this job description.
        Return as JSON array of skill objects:
        
        [
          {
            "name": "JavaScript",
            "category": "Technical",
            "required": true,
            "level": "Intermediate"
          }
        ]
        
        Job Description:
        ${jobDescription}
        
        Return only the JSON array.
      `;

      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert at extracting skills from job descriptions. Be thorough and accurate.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      });

      const content = response.choices[0].message.content.trim();
      
      try {
        return JSON.parse(content);
      } catch (parseError) {
        const jsonMatch = content.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
        throw new Error('Failed to parse job skills response');
      }
    } catch (error) {
      console.error('Error extracting job skills:', error);
      throw new Error(`Failed to extract job skills: ${error.message}`);
    }
  }
}

module.exports = new OpenAIService();
