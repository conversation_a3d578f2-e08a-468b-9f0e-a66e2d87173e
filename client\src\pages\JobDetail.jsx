import React from 'react'
import { useParams } from 'react-router-dom'
import { Briefcase } from 'lucide-react'

const JobDetail = () => {
  const { id } = useParams()

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="text-center">
            <Briefcase className="mx-auto h-12 w-12 text-gray-400" />
            <h2 className="mt-2 text-lg font-medium text-gray-900">Job Details</h2>
            <p className="mt-1 text-sm text-gray-600">
              Job ID: {id}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default JobDetail
